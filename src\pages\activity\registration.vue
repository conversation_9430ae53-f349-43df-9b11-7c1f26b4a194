<template>
  <div class="registration-page">
    <!-- 导航栏 -->
    <van-nav-bar
      title="活动报名"
      left-arrow
      @click-left="goBack"
    />

    <!-- 活动信息部分 -->
    <div class="activity-info-section">
      <div class="section-header">
        <div class="header-left">
          <div class="orange-bar"></div>
          <span class="section-title">活动信息</span>
        </div>
      </div>
      
      <!-- 收起状态：只显示前2条关键信息 -->
      <div class="activity-info-content" v-show="!isActivityInfoExpanded">
        <div class="info-item">
          <span class="label">活动编号：</span>
          <span class="value">{{ activity.activityCode || 'XXXXXXXX' }}</span>
        </div>
        <div class="info-item">
          <span class="label">活动名称：</span>
          <span class="value">{{ activity.title || 'XXXXXXXXXXXXXXXXXXXXXX' }}</span>
        </div>
        <div class="expand-trigger" @click="toggleActivityInfo">
          <van-icon 
            :name="isActivityInfoExpanded ? 'arrow-up' : 'arrow-down'" 
            class="expand-icon"
          />
        </div>
      </div>
      
      <!-- 展开状态：显示所有信息 -->
      <div class="activity-info-content" v-show="isActivityInfoExpanded">
        <div class="info-item">
          <span class="label">活动编号：</span>
          <span class="value">{{ activity.activityCode || 'XXXXXXXX' }}</span>
        </div>
        <div class="info-item">
          <span class="label">活动名称：</span>
          <span class="value">{{ activity.title || 'XXXXXXXXXXXXXXXXXXXXXX' }}</span>
        </div>
        <div class="info-item">
          <span class="label">活动标题：</span>
          <span class="value">{{ activity.subtitle || 'XXXXXXXXXXXXXXXXXXXXXX' }}</span>
        </div>
        <div class="info-item">
          <span class="label">活动时间：</span>
          <span class="value">{{ formatActivityTime(activity.activityTime) }}</span>
        </div>
        <div class="info-item">
          <span class="label">活动地址：</span>
          <span class="value">{{ formatActivityAddress(activity.location) }}</span>
        </div>
        <div class="info-item">
          <span class="label">活动须知：</span>
          <span class="value">{{ activity.notice || '暂无须知' }}</span>
        </div>
        <div class="expand-trigger" @click="toggleActivityInfo">
          <van-icon 
            :name="isActivityInfoExpanded ? 'arrow-up' : 'arrow-down'" 
            class="expand-icon"
          />
        </div>
      </div>
    </div>

    <!-- 报名信息部分 -->
    <div class="registration-form-section">
      <div class="section-header">
        <div class="header-left">
          <div class="orange-bar"></div>
          <span class="section-title">请填写下列报名信息</span>
        </div>
      </div>

      <div class="registration-form">
        <!-- 预设项目 -->
        <div class="form-group">
          <!-- 姓名 -->
          <van-field
            v-model="formData.name"
            placeholder="请输入您的姓名"
            :error-message="fieldErrors.name"
            @blur="validateField('name')"
          >
            <template #label>
              <span>姓名</span>
              <span v-if="isFieldRequired('name')" class="required-star">*</span>
            </template>
          </van-field>
          
          <!-- 手机号 -->
          <van-field
            v-model="formData.phone"
            placeholder="请输入手机号"
            type="tel"
            :error-message="fieldErrors.phone"
            :readonly="true"
            @blur="validateField('phone')"
          >
            <template #label>
              <span>手机号</span>
              <span v-if="isFieldRequired('phone')" class="required-star">*</span>
            </template>
          </van-field>
          
          <!-- 证件类型 -->
          <van-field
            v-model="formData.cardType"
            placeholder="请选择证件类型"
            readonly
            is-link
            @click="showCardTypePicker = true"
            :error-message="fieldErrors.cardType"
          >
            <template #label>
              <span>证件类型</span>
              <span v-if="isFieldRequired('cardType')" class="required-star">*</span>
            </template>
          </van-field>
          
          <!-- 身份证号码 -->
          <van-field
            v-model="formData.idCard"
            placeholder="请输入身份证号码"
            :error-message="fieldErrors.idCard"
            @blur="validateField('idCard')"
          >
            <template #label>
              <span>身份证号码</span>
              <span v-if="isFieldRequired('idCard')" class="required-star">*</span>
            </template>
          </van-field>
          
          <!-- 年龄 -->
          <van-field
            v-model="formData.age"
            placeholder="请输入年龄"
            type="number"
            :error-message="fieldErrors.age"
            @blur="validateField('age')"
          >
            <template #label>
              <span>年龄</span>
              <span v-if="isFieldRequired('age')" class="required-star">*</span>
            </template>
          </van-field>
          
          <!-- 性别 -->
          <van-field :error-message="fieldErrors.gender">
            <template #label>
              <span>性别</span>
              <span v-if="isFieldRequired('gender')" class="required-star">*</span>
            </template>
            <template #input>
              <div class="gender-options">
                <label class="radio-option" v-for="option in genderOptions" :key="option">
                  <input 
                    type="radio" 
                    :value="option" 
                    v-model="formData.gender"
                    @change="validateField('gender')"
                  />
                  <span class="radio-label">{{ option }}</span>
                </label>
              </div>
            </template>
          </van-field>
          
          <!-- 身高 -->
          <van-field
            v-model="formData.height"
            placeholder="请输入身高"
            type="digit"
            :error-message="fieldErrors.height"
            @blur="validateField('height')"
          >
            <template #label>
              <span>身高</span>
              <span v-if="isFieldRequired('height')" class="required-star">*</span>
            </template>
            <template #right-icon>
              <span class="unit">cm</span>
            </template>
          </van-field>
          
          <!-- 体重 -->
          <van-field
            v-model="formData.weight"
            placeholder="请输入体重"
            type="digit"
            :error-message="fieldErrors.weight"
            @blur="validateField('weight')"
          >
            <template #label>
              <span>体重</span>
              <span v-if="isFieldRequired('weight')" class="required-star">*</span>
            </template>
            <template #right-icon>
              <span class="unit">kg</span>
            </template>
          </van-field>
          
          <!-- 学历 -->
          <van-field
            v-model="formData.education"
            placeholder="请选择学历"
            readonly
            is-link
            @click="showEducationPicker = true"
            :error-message="fieldErrors.education"
          >
            <template #label>
              <span>学历</span>
              <span v-if="isFieldRequired('education')" class="required-star">*</span>
            </template>
          </van-field>
          
          <!-- 携带亲属 -->
          <van-field :error-message="fieldErrors.familyMembers">
            <template #label>
              <span>携带亲属</span>
              <span v-if="isFieldRequired('familyMembers')" class="required-star">*</span>
            </template>
            <template #input>
              <div class="stepper-group">
                <div class="stepper-item">
                  <van-stepper 
                    v-model="formData.adultCount" 
                    :min="0" 
                    :max="maxFamilyMembers"
                    @change="validateFamilyMembers"
                  />
                  <span class="stepper-label">大</span>
                </div>
                <div class="stepper-item">
                  <van-stepper 
                    v-model="formData.childCount" 
                    :min="0" 
                    :max="maxFamilyMembers"
                    @change="validateFamilyMembers"
                  />
                  <span class="stepper-label">小</span>
                </div>
              </div>
            </template>
          </van-field>
        </div>

        <!-- 自增项目 -->
        <div class="form-group" v-if="customFields.length > 0">
          <div 
            v-for="field in customFields" 
            :key="field.id"
            class="custom-field"
          >
            <van-field
              v-if="field.type === 'text'"
              v-model="formData.customFields[field.id]"
              :placeholder="`请输入${field.label}`"
              :error-message="fieldErrors[`custom_${field.id}`]"
              @blur="validateCustomField(field.id)"
            >
              <template #label>
                <span>{{ field.label }}</span>
                <span v-if="field.required" class="required-star">*</span>
              </template>
            </van-field>
            
            <van-field
              v-else-if="field.type === 'textarea'"
              v-model="formData.customFields[field.id]"
              :placeholder="`请输入${field.label}`"
              type="textarea"
              rows="3"
              :error-message="fieldErrors[`custom_${field.id}`]"
              @blur="validateCustomField(field.id)"
            >
              <template #label>
                <span>{{ field.label }}</span>
                <span v-if="field.required" class="required-star">*</span>
              </template>
            </van-field>
            
            <van-field
              v-else-if="field.type === 'select'"
              v-model="formData.customFields[field.id]"
              :placeholder="`请选择${field.label}`"
              readonly
              is-link
              @click="() => showCustomFieldPicker(field)"
              :error-message="fieldErrors[`custom_${field.id}`]"
            >
              <template #label>
                <span>{{ field.label }}</span>
                <span v-if="field.required" class="required-star">*</span>
              </template>
            </van-field>
          </div>
        </div>
      </div>
    </div>

    <!-- 提交按钮 -->
    <div class="submit-section">
      <van-button
        type="primary"
        size="large"
        :loading="submitting"
        :disabled="submitting"
        @click="submitRegistration"
        class="submit-button"
      >
        {{ submitting ? '提交中...' : '提交报名' }}
      </van-button>
    </div>

    <!-- 选择器弹窗 -->
    <!-- 证件类型选择器 -->
    <van-popup v-model="showCardTypePicker" position="bottom">
      <van-picker
        :columns="cardTypeOptions"
        @confirm="onCardTypeConfirm"
        @cancel="showCardTypePicker = false"
      />
    </van-popup>


    <!-- 学历选择器 -->
    <van-popup v-model="showEducationPicker" position="bottom">
      <van-picker
        :columns="educationOptions"
        @confirm="onEducationConfirm"
        @cancel="showEducationPicker = false"
      />
    </van-popup>

    <!-- 自定义字段选择器 -->
    <van-popup v-model="showCustomFieldPickerDialog" position="bottom">
      <van-picker
        :columns="currentCustomFieldOptions"
        @confirm="onCustomFieldConfirm"
        @cancel="onCustomFieldCancel"
      />
    </van-popup>

    <!-- 成功提示弹窗 -->
    <van-dialog
      v-model="showSuccessDialog"
      title="报名成功"
      message="恭喜您报名成功！我们会尽快与您联系。"
      confirm-button-text="知道了"
      @confirm="onSuccessConfirm"
    />
  </div>
</template>

<script>
import Vue from 'vue';
import {
  NavBar,
  Icon,
  Button,
  Field,
  Stepper,
  Popup,
  Picker,
  Dialog,
  Toast,
  ActionSheet
} from 'vant';

Vue.use(NavBar)
  .use(Icon)
  .use(Button)
  .use(Field)
  .use(Stepper)
  .use(Popup)
  .use(Picker)
  .use(Dialog)
  .use(Toast)
  .use(ActionSheet);

export default {
  name: 'ActivityRegistration',
  data() {
    return {
      activity: {},
      isActivityInfoExpanded: false, // 默认折叠
      submitting: false,
      showSuccessDialog: false,
      
      // 表单数据
      formData: {
        name: '周杰伦',
        phone: '15888888888', // 默认取登录人员手机号，不支持修改
        cardType: '',
        idCard: '330106202508283324',
        age: '18',
        gender: '男',
        height: '',
        weight: '',
        education: '',
        adultCount: 0,
        childCount: 0,
        customFields: {}
      },
      
      // 字段错误信息
      fieldErrors: {},
      
      // 选择器相关
      showCardTypePicker: false,
      showEducationPicker: false,
      showCustomFieldPickerDialog: false,
      currentCustomField: null,
      
      // 选项数据
      cardTypeOptions: ['身份证', '护照', '港澳通行证', '台胞证'],
      genderOptions: ['男', '女', '其他'],
      educationOptions: ['小学', '初中', '高中', '大专', '本科', '硕士', '博士'],
      
      // 自定义字段
      customFields: [],
      maxFamilyMembers: 5, // 携带人数上限
      
      // 字段必填配置（从后端获取）
      fieldRequiredConfig: {
        name: true,        // 姓名必填
        phone: true,       // 手机号必填（默认填充，不支持修改）
        cardType: false,   // 证件类型
        idCard: true,      // 身份证号码必填
        age: true,         // 年龄必填
        gender: true,      // 性别必填
        height: false,     // 身高
        weight: false,     // 体重
        education: false,  // 学历
        familyMembers: false // 携带亲属
      }
    };
  },
  
  computed: {
    currentCustomFieldOptions() {
      if (!this.currentCustomField) return [];
      return this.currentCustomField.options || [];
    },
    
    currentCustomFieldActions() {
      if (!this.currentCustomField) {
        console.log('currentCustomField is null');
        return [];
      }
      const options = this.currentCustomField.options || [];
      console.log('currentCustomFieldActions:', options);
      return options.map(option => ({
        name: option,
        value: option
      }));
    }
  },
  
  mounted() {
    this.loadActivityData();
    this.loadRegistrationConfig();
  },
  
  methods: {
    // 返回上一页
    goBack() {
      this.$router.go(-1);
    },
    
    // 加载活动数据
    async loadActivityData() {
      try {
        const activityId = this.$route.params.id;
        const response = await this.$api.getActivityDetail(activityId);
        
        if (response && response.success === 1) {
          const data = response.value;
          this.activity = {
            id: activityId,
            activityCode: `ACT${activityId}`,
            title: data.actTitle,
            subtitle: data.actSubtitle || data.actTitle,
            activityTime: data.actTime,
            location: data.locationString,
            notice: data.actNotice
          };
        }
      } catch (error) {
        console.error('加载活动数据失败:', error);
        // 使用模拟数据
        this.activity = {
          id: this.$route.params.id,
          activityCode: `ACT${this.$route.params.id}`,
          title: 'XXXXXXXXXXXXXXXXXXXXXX',
          subtitle: 'XXXXXXXXXXXXXXXXXXXXXX',
          activityTime: '2024.05.01 星期三 09:30 至 2024.05.30 星期四 18:30',
          location: '杭州市西湖区文新社区（杭州市西湖区文新社区文新社区文新社区）',
          notice: '请仔细阅读活动须知，确保您了解并同意所有条款后再进行报名。'
        };
      }
    },
    
    // 加载报名配置
    async loadRegistrationConfig() {
      try {
        // TODO: 调用接口获取报名配置
        // 模拟数据
        this.customFields = [
          {
            id: 'community',
            label: '社区',
            type: 'text',
            required: true
          },
          {
            id: 'address',
            label: '地址',
            type: 'textarea',
            required: true
          },
          {
            id: 'occupation',
            label: '职业',
            type: 'select',
            required: false,
            options: ['医生', '教师', '工程师', '学生', '自由职业', '其他']
          }
        ];
        
        // 初始化自定义字段数据
        this.customFields.forEach(field => {
          this.$set(this.formData.customFields, field.id, '');
        });
        
        // 加载字段必填配置
        await this.loadFieldRequiredConfig();
      } catch (error) {
        console.error('加载报名配置失败:', error);
      }
    },
    
    // 加载字段必填配置
    async loadFieldRequiredConfig() {
      try {
        // TODO: 从后端获取字段必填配置
        // 这里应该调用接口获取活动相关的字段配置
        // const response = await this.$api.getActivityFieldConfig(this.activity.id);
        
        // 模拟后端返回的配置数据
        const backendConfig = {
          name: true,        // 姓名必填
          phone: true,       // 手机号必填（默认填充，不支持修改）
          cardType: false,   // 证件类型
          idCard: true,      // 身份证号码必填
          age: true,         // 年龄必填
          gender: true,      // 性别必填
          height: false,     // 身高
          weight: false,     // 体重
          education: false,  // 学历
          familyMembers: false // 携带亲属
        };
        
        // 更新字段必填配置
        this.fieldRequiredConfig = { ...this.fieldRequiredConfig, ...backendConfig };
      } catch (error) {
        console.error('加载字段必填配置失败:', error);
        // 使用默认配置
      }
    },
    
    // 切换活动信息展开/折叠
    toggleActivityInfo() {
      this.isActivityInfoExpanded = !this.isActivityInfoExpanded;
    },
    
    // 格式化活动时间
    formatActivityTime(timeString) {
      if (!timeString) return '时间待定';
      return timeString;
    },
    
    // 格式化活动地址
    formatActivityAddress(addressString) {
      if (!addressString) return '地址待定';
      return addressString;
    },
    
    // 检查字段是否必填
    isFieldRequired(fieldName) {
      return this.fieldRequiredConfig[fieldName] || false;
    },
    
    
    // 验证单个字段
    validateField(fieldName) {
      const value = this.formData[fieldName];
      let error = '';
      
      switch (fieldName) {
        case 'name':
          if (!value || !value.trim()) {
            error = '请填写姓名';
          }
          break;
        case 'phone':
          if (!value || !value.trim()) {
            error = '请填写手机号';
          } else if (!/^1[3-9]\d{9}$/.test(value)) {
            error = '手机号格式不正确';
          }
          break;
        case 'idCard':
          if (!value || !value.trim()) {
            error = '请填写身份证号码';
          } else if (!/^[1-9]\d{5}(18|19|20)\d{2}((0[1-9])|(1[0-2]))(([0-2][1-9])|10|20|30|31)\d{3}[0-9Xx]$/.test(value)) {
            error = '身份证号码格式不正确';
          }
          break;
        case 'age':
          if (!value || !value.trim()) {
            error = '请填写年龄';
          } else if (isNaN(value) || value < 1 || value > 120) {
            error = '请输入有效的年龄';
          }
          break;
        case 'gender':
          if (!value || !value.trim()) {
            error = '请选择性别';
          }
          break;
        case 'height':
          if (value && (isNaN(value) || value < 50 || value > 250)) {
            error = '请输入有效的身高';
          }
          break;
        case 'weight':
          if (value && (isNaN(value) || value < 10 || value > 300)) {
            error = '请输入有效的体重';
          }
          break;
      }
      
      this.$set(this.fieldErrors, fieldName, error);
      return !error;
    },
    
    // 验证自定义字段
    validateCustomField(fieldId) {
      const field = this.customFields.find(f => f.id === fieldId);
      if (!field) return true;
      
      const value = this.formData.customFields[fieldId];
      let error = '';
      
      if (field.required && (!value || !value.trim())) {
        error = `请填写${field.label}`;
      }
      
      this.$set(this.fieldErrors, `custom_${fieldId}`, error);
      return !error;
    },
    
    // 验证携带亲属
    validateFamilyMembers() {
      const total = this.formData.adultCount + this.formData.childCount;
      let error = '';
      
      if (total > this.maxFamilyMembers) {
        error = `携带人数不能超过${this.maxFamilyMembers}人`;
        this.formData.adultCount = Math.min(this.formData.adultCount, this.maxFamilyMembers);
        this.formData.childCount = Math.min(this.formData.childCount, this.maxFamilyMembers - this.formData.adultCount);
      }
      
      this.$set(this.fieldErrors, 'familyMembers', error);
      return !error;
    },
    
    // 选择器确认事件
    onCardTypeConfirm(value) {
      this.formData.cardType = value;
      this.showCardTypePicker = false;
      this.validateField('cardType');
    },
    
    
    onEducationConfirm(value) {
      this.formData.education = value;
      this.showEducationPicker = false;
      this.validateField('education');
    },
    
    // 显示自定义字段选择器
    showCustomFieldPicker(field) {
      console.log('showCustomFieldPicker called with:', field);
      this.currentCustomField = field;
      this.showCustomFieldPickerDialog = true;
      console.log('showCustomFieldPickerDialog set to:', this.showCustomFieldPickerDialog);
    },
    
    onCustomFieldConfirm(value, index) {
      console.log('onCustomFieldConfirm called with:', value, index);
      console.log('typeof value:', typeof value);
      
      // 处理不同的参数格式
      let selectedValue = value;
      if (typeof value === 'object' && value !== null) {
        selectedValue = value.value || value.text || value.name;
      }
      
      console.log('selectedValue:', selectedValue);
      
      if (this.currentCustomField && selectedValue) {
        this.$set(this.formData.customFields, this.currentCustomField.id, selectedValue);
        this.validateCustomField(this.currentCustomField.id);
      }
      
      this.showCustomFieldPickerDialog = false;
    },
    
    onCustomFieldCancel() {
      console.log('onCustomFieldCancel called');
      this.showCustomFieldPickerDialog = false;
    },
    
    // 提交报名
    async submitRegistration() {
      // 防止重复提交
      if (this.submitting) return;
      
      // 验证所有必填项
      let hasError = false;
      
      // 验证预设字段
      Object.keys(this.fieldRequiredConfig).forEach(field => {
        if (this.isFieldRequired(field) && !this.validateField(field)) {
          hasError = true;
        }
      });
      
      // 验证自定义字段
      this.customFields.forEach(field => {
        if (field.required && !this.validateCustomField(field.id)) {
          hasError = true;
        }
      });
      
      // 验证携带亲属
      if (!this.validateFamilyMembers()) {
        hasError = true;
      }
      
      if (hasError) {
        Toast('请填写带*的项目');
        return;
      }
      
      // 验证字段合法性
      if (!this.validateField('name') || !this.validateField('idCard')) {
        Toast('姓名或身份证填写错误，请检查');
        return;
      }
      
      this.submitting = true;
      
      try {
        // 构建提交数据
        const submitData = {
          activityId: this.activity.id,
          ...this.formData,
          customFields: this.formData.customFields
        };
        
        // 调用报名接口
        const response = await this.$api.registerActivity(submitData);
        
        if (response && response.success === 1) {
          this.showSuccessDialog = true;
        } else {
          Toast(response?.respDesc || '报名失败，请重试');
        }
      } catch (error) {
        console.error('报名失败:', error);
        Toast('报名失败，请重试');
      } finally {
        this.submitting = false;
      }
    },
    
    // 成功确认
    onSuccessConfirm() {
      this.showSuccessDialog = false;
      // 返回来源页
      this.$router.go(-1);
    }
  }
};
</script>

<style lang="less" scoped>
.registration-page {
  min-height: 100vh;
  background: #f5f5f5;
  padding-bottom: 80px;
}

.activity-info-section,
.registration-form-section {
  background: white;
  margin: 8px 16px;
  border-radius: 6px;
  overflow: hidden;
}

.section-header {
  display: flex;
  align-items: center;
  padding: 16px;
  position: relative;
  
  .header-left {
    display: flex;
    align-items: center;
    width: 100%;
    
    .orange-bar {
      position: absolute;
      left: 0;
      top: 50%;
      transform: translateY(-50%);
      width: 4.5px;
      height: 15px;
      background: #C28D4B;
      border-radius: 2.25px;
      margin-right: 3px;
    }
    
    .section-title {
      font-family: 'PingFang SC Semibold', 'PingFang SC', 'Microsoft YaHei', Arial, sans-serif;
      font-weight: 600;
      font-size: 16px;
      color: #373737;
      margin-left: 3px;
      text-align: left;
    }
  }
}

.activity-info-content {
  padding: 0 16px 16px;
  
  .info-item {
    display: flex;
    padding: 12px 0;
    border-bottom: 1px solid #f0f0f0;
    
    .label {
      font-family: 'PingFang SC', 'PingFang SC Medium', 'Microsoft YaHei', Arial, sans-serif;
      min-width: 80px;
      font-size: 14px;
      color: #373737;
      flex-shrink: 0;
    }
    
    .value {
      flex: 1;
      font-size: 14px;
      color: #333;
      word-break: break-all;
      line-height: 1.4;
      text-align: right;
    }
  }
  
  .expand-trigger {
    display: flex;
    justify-content: center;
    padding: 12px 0;
    border-top: 1px solid #f0f0f0;
    cursor: pointer;
    
    .expand-icon {
      color: #999;
      font-size: 16px;
    }
  }
}

.registration-form {
  padding: 3px;
  
  .form-group {
    margin-bottom: 16px;
    
    &:last-child {
      margin-bottom: 0;
    }
  }
  
  .unit {
    color: #999;
    font-size: 14px;
  }
  
  .stepper-group {
    display: flex;
    gap: 20px;
    
    .stepper-item {
      display: flex;
      align-items: center;
      gap: 4px;
      
      .stepper-label {
        font-family: 'PingFang SC', 'PingFang SC Medium', 'Microsoft YaHei', Arial, sans-serif;
        font-size: 14px;
        color: #373737;
        min-width: 15px;
      }
    }
  }
  
  // 修改步进器内部布局，让输入框和标签在同一行
  :deep(.van-stepper) {
    display: flex;
    align-items: center;
    
    .van-stepper__input {
      width: 25px;
      height: 20px;
      font-size: 11px;
      margin: 0 3px;
    }
    
    .van-stepper__button {
      width: 20px;
      height: 20px;
      font-size: 11px;
    }
    
    .van-stepper__minus,
    .van-stepper__plus {
      width: 20px;
      height: 20px;
      font-size: 11px;
    }
  }
  
  .custom-field {
    margin-bottom: 16px;
    
    &:last-child {
      margin-bottom: 0;
    }
  }
  
  .gender-options {
    display: flex;
    gap: 24px;
    
    .radio-option {
      display: flex;
      align-items: center;
      cursor: pointer;
      
      input[type="radio"] {
        margin-right: 8px;
        width: 14px;
        height: 14px;
        accent-color: #1989fa;
      }
      
      .radio-label {
        font-size: 13px;
        color: #333;
      }
    }
  }
}

.submit-section {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  padding: 16px;
  background: white;
  border-top: 1px solid #f0f0f0;
  
  .submit-button {
    width: 100%;
    height: 48px;
    background: linear-gradient(90deg, #C08A48 0%, #EDC391 100%);
    border: none;
    border-radius: 24px;
    font-size: 16px;
    font-weight: 500;
    color: white;
  }
}

:deep(.van-field) {
  margin-bottom: 16px;
}

:deep(.van-field__label) {
  color: #333;
  font-size: 14px;
  text-align: left;
  justify-content: flex-start;
  width: 80px;
  min-width: 80px;
  flex-shrink: 0;
  
  // 必填星号样式
  .required-star {
    color: #ff6b35;
    font-weight: bold;
    margin-left: 4px;
  }
}

:deep(.van-field__control) {
  font-size: 14px;
}

// 单行文本框右对齐
:deep(.van-field:not(.van-field--textarea) .van-field__control) {
  text-align: right;
}

// 多行文本框保持左对齐
:deep(.van-field--textarea .van-field__control) {
  text-align: left;
}

:deep(.van-field__error-message) {
  color: #ff6b35;
  font-size: 12px;
}

:deep(.van-stepper) {
  .van-stepper__input {
    width: 35px;
    height: 28px;
    font-size: 13px;
  }
  
  .van-stepper__button {
    width: 28px;
    height: 28px;
    font-size: 14px;
  }
}
</style>
