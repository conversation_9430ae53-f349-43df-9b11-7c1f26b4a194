<template>
    <div class="content">
        <div class="find">
            <p class="prompt">找医生</p>
            <div class="find-tabs">
                <span @click="active = 1" :class="{ active: active == 1 }"
                    >按科室</span
                >
                <span @click="active = 0" :class="{ active: active == 0 }"
                    >按病症</span
                >
            </div>
        </div>
        <div
            class="depart"
            v-if="active == 1"
            :style="{ height: isZfb ? '1.95rem' : '1.5rem' }"
        >
            <div class="depart-inner">
                <div
                    class="depart-item"
                    v-for="(list, index) in dataList"
                    :key="index"
                >
                    <div
                        v-for="(item, inner) in list"
                        :key="inner"
                        @click="toSearchDoctorList(item, 1)"
                    >
                        <img :src="item.img" alt="" />
                        <p class="desc">{{ item.applicationName }}</p>
                    </div>
                </div>
            </div>
            <div class="getMore" @click="jumpUrl()" v-if="active == 1 && isZfb">
                查看更多科室
                <van-icon name="arrow" :color="$store.state.primaryColor" />
            </div>
        </div>
        <div class="disease" v-if="active == 0">
            <!-- <div
        class="disease-item"
        v-for="(item, index) in semeList"
        :key="index"
        @click="toSearchDoctorList(item, 0)"
      >
        <img :src="item.img" alt="" />
        <p>{{ item.applicationName }}</p>
      </div>
      <div class="getMore" @click="toDiseaseFind()">
        查看更多
        <van-icon name="arrow" :color="$store.state.primaryColor" />
      </div> -->
            <van-swipe class="my-swipe" :loop="false" indicator-color="#01CDA7">
                <van-swipe-item
                    class="disease-item"
                    v-for="(list, index) in dataList"
                    :key="index"
                >
                    <div
                        v-for="(item, inner) in list"
                        :key="inner"
                        @click="toSearchDoctorList(item, 0)"
                        style="width: 25%"
                    >
                        <img :src="item.img" alt="" />
                        <p class="desc">{{ item.applicationName }}</p>
                    </div>
                </van-swipe-item>
            </van-swipe>
        </div>
    </div>
</template>

<script>
import tools from "@/util/tools";
import { Tab, Tabs } from "vant";
import StageItem from "./stageItem.vue";

export default {
    name: "Find",
    props: {
        data: {
            type: Object,
            default: () => {},
        },
    },
    components: {
        [Tab.name]: Tab,
        [Tabs.name]: Tabs,
        StageItem,
    },
    data() {
        return {
            isZfb: navigator.userAgent.indexOf("AliApp") > -1,
            active: 1,
            departmentUrl:
                "https://www.hfi-health.com:28181/medical-advice/#/specialty?title=%E7%89%B9%E8%89%B2%E4%B8%93%E7%A7%91&origin=hlwywMini",
            semeListR: [],
            semeList: [
                {
                    applicationName: "肺结节",
                    img: require("@/images/img/home_bzc_fjj.png"),
                },
                {
                    applicationName: "甲状腺结节",
                    img: require("@/images/img/home_bzc_jzxjj.png"),
                },
                {
                    applicationName: "胆囊结石",
                    img: require("@/images/img/home_bzc_dnjs.png"),
                },
                {
                    applicationName: "胆囊息肉",
                    img: require("@/images/img/home_bzc_dnxr.png"),
                },
                {
                    applicationName: "肝肾囊肿",
                    img: require("@/images/img/home_bzc_gsnz.png"),
                },
                {
                    applicationName: "尿蛋白尿隐血",
                    img: require("@/images/img/home_bzc_ndbyx.png"),
                },
                {
                    applicationName: "乳腺结节",
                    img: require("@/images/img/home_bzc_rxjj.png"),
                },
                {
                    applicationName: "月经不调",
                    img: require("@/images/img/home_bzc_yjbt.png"),
                },
                {
                    applicationName: "儿童生长发育",
                    img: require("@/images/img/home_bzc_etszfy.png"),
                },
                {
                    applicationName: "小儿喂养",
                    img: require("@/images/img/home_bzc_xewy.png"),
                },
                {
                    applicationName: "腺样体肥大",
                    img: require("@/images/img/home_bzc_xytfd.png"),
                },
                {
                    applicationName: "老年痴呆",
                    img: require("@/images/img/home_bzc_lncd.png"),
                },
                {
                    applicationName: "失眠焦虑",
                    img: require("@/images/img/home_bzc_smjl.png"),
                },
                {
                    applicationName: "抑郁障碍",
                    img: require("@/images/img/home_bzc_yyza.png"),
                },
                {
                    applicationName: "儿童心理",
                    img: require("@/images/img/home_bzc_etxl.png"),
                },
                // {
                //   applicationName: "鼻炎",
                //   img: require("@/images/img/home_bzc_by.png"),
                // },
                // {
                //   applicationName: "失眠",
                //   img: require("@/images/img/home_bzc_sm.png"),
                // },
                // {
                //   applicationName: "咳嗽",
                //   img: require("@/images/img/home_bzc_ks.png"),
                // },
                // {
                //   applicationName: "湿疹",
                //   img: require("@/images/img/home_bzc_sz.png"),
                // },
                // {
                //   applicationName: "腰腿痛",
                //   img: require("@/images/img/home_bzc_ytt.png"),
                // },
                // {
                //   applicationName: "哮喘",
                //   img: require("@/images/img/home_bzc_xc.png"),
                // },
                // {
                //   applicationName: "高血压",
                //   img: require("@/images/img/home_bzc_gxy.png"),
                // },
                // {
                //   applicationName: "糖尿病",
                //   img: require("@/images/img/home_bzc_tnb.png"),
                // },
            ],
            deptListR: [],
            deptList: [
                {
                    applicationName: "皮肤科",
                    img: require("@/images/img/home_ks_pfk.png"),
                    deptId: "A13",
                },
                {
                    applicationName: "耳鼻咽喉科",
                    img: require("@/images/img/home_ks_ebyhk.png"),
                    deptId: "A11",
                },
                {
                    applicationName: "产科",
                    img: require("@/images/img/home_ks_ck.png"),
                    deptId: "A05",
                },
                {
                    applicationName: "呼吸内科",
                    img: require("@/images/img/home_ks_hxnk.png"),
                    deptId: "A03",
                    deptClassCode: "A03.01",
                },
                {
                    applicationName: "儿科",
                    img: require("@/images/img/home_ks_ek.png"),
                    deptId: "A07",
                },
                {
                    applicationName: "消化内科",
                    img: require("@/images/img/home_ks_xhnk.png"),
                    deptId: "A03",
                    deptClassCode: "A03.02",
                },
                {
                    applicationName: "中医科",
                    img: require("@/images/img/home_ks_zyk.png"),
                    deptId: "A50",
                },
                {
                    applicationName: "口腔科",
                    img: require("@/images/img/home_ks_kqk.png"),
                    deptId: "A12",
                },
            ],
            dataList: [[], []],
        };
    },
    created() {
        this.deptListR = this.handleData(this.deptList);
        this.semeListR = this.handleData(this.semeList);
        this.dataList = this.deptListR;
    },

    beforeDestroy() {
        console.log('Find组件即将销毁，清理轮播资源');
        this.cleanupSwipe();
    },
    mounted() {
        // var tList = [[], []];
        // var odd = [];
        // var even = [];
        // for (let j = 0; j < this.deptList.length; j++) {
        //   let ele = this.deptList[j];
        //   if (j % 2 == 0) {
        //     tList[0].push(ele);
        //   } else {
        //     tList[1].push(ele);
        //   }
        // }
        // this.deptListR = tList; //[even, odd];
    },
    watch: {
        active: {
            handler(newVal, oldVal) {
                if (newVal == 1) {
                    //科室
                    this.dataList = this.deptListR;
                } else {
                    //病症
                    this.dataList = this.semeListR;
                }
            },
        },
    },
    methods: {
        handleData(list) {
            var tList = [[], []];
            var odd = [];
            var even = [];
            for (let j = 0; j < list.length; j++) {
                let ele = list[j];
                if (j % 2 == 0) {
                    tList[0].push(ele);
                } else {
                    tList[1].push(ele);
                }
            }
            return tList; //[even, odd];
        },
        toSearchDoctorList(val, type) {
            var query = {};
            type = this.active;
            if (type == 0) {
                tools.handleSetPoint({
                    trackingContent: `首页|${val.applicationName}|找医生`,
                });
                //按病症
                query["searchValue"] = val.applicationName;
            } else {
                tools.handleSetPoint({
                    trackingContent: `首页|${val.deptId}|找医生`,
                });
                //按科室
                query["department"] = val.deptId;
                val.deptClassCode
                    ? (query["deptClassCode"] = val.deptClassCode)
                    : "";
            }
            if (this.$store.state.isWjAliMini) {
                debugger;
                let str = "";
                for (let i in query) {
                    str = "&" + i + "=" + query[i];
                }
                // 跳转到另一个小程序另一个页面
                my.navigateTo({
                    url:
                        "/pages/wjIndex/wjIndex?secUrl=" +
                        encodeURIComponent(
                            window.location.origin +
                                window.location.pathname +
                                "#/searchDoctorList?origin=wjAliMini" +
                                str
                        ), // url详解请见【路由使用须知】
                });
                /* this.$router.push({
                    path: "/search",
                    query: { origin: "wjAliMini" },
                }); */
            } else {
                this.$router.push({
                    path: "searchDoctorList",
                    query,
                });
            }
        },
        jumpUrl() {
            if (this.active == 1) {
                if (this.$store.state.isWjAliMini) {
                    let jumpUrl = `/pages/index/index?returnURL=${encodeURIComponent(
                        this.departmentUrl
                    )}`;
                    my.postMessage({
                        action: "gotoMini",
                        appId: "2021002138635948",
                        url: jumpUrl,
                        authStatus: "2",
                    });
                } else {
                    tools.jumpUrl(this.departmentUrl, "2");
                }
            } else {
            }
        },
        toDiseaseFind() {
            this.$router.push({ path: "diseaseFind" });
        },

        // 清理轮播组件
        cleanupSwipe() {
            try {
                console.log('Find: 清理轮播组件');
                // 查找轮播组件并停止自动播放
                const swipeElements = this.$el ? this.$el.querySelectorAll('.van-swipe') : [];
                swipeElements.forEach((element, index) => {
                    console.log(`Find: 清理第 ${index + 1} 个轮播组件`);
                    const vueComponent = element.__vue__;
                    if (vueComponent && typeof vueComponent.stopAutoplay === 'function') {
                        vueComponent.stopAutoplay();
                    }
                });
            } catch (error) {
                console.error('Find: 清理轮播组件时出错', error);
            }
        },
    },
};
</script>

<style lang="less" scoped>
.content {
    font-family: "PingFang SC";
    .find {
        background-image: url("@/images/img/home_find_bg.png");
        background-size: 100% 100%;
        padding: 15px 13px 23px 15px;
        display: flex;
        justify-content: space-between;
        align-items: center;
        line-height: 1;
        .prompt {
            font-size: 18px;
            font-weight: 500;
            color: #000000;
        }
        &-tabs {
            display: flex;
            font-size: 13px;
            font-weight: 400;
            color: #333333;
            span {
                display: inline-block;
                padding: 0 10px;
                border-right: 1px solid #f1f1f1;
            }
            :last-child {
                padding-right: 0;
                border-right: none;
            }
        }
        .active {
            color: #3ebfa0;
        }
    }

    .disease {
        display: flex;
        flex-wrap: wrap;
        align-items: center;
        padding-bottom: 10px;
        &-item {
            // flex-shrink: 0;
            // width: 25%;
            // text-align: center;
            // margin-bottom: 20px;
            // font-size: 0;

            display: flex;
            width: 100%;
            // height: 150px;
            flex-wrap: wrap;
            div {
                flex-shrink: 0;
                width: 25%;
                text-align: center;
                margin-bottom: 20px;
                font-size: 0;
            }
            img {
                width: 29px;
                background-size: 100% auto;
                margin-bottom: 12px;
            }
            p {
                font-size: 14px;
                text-align: center;
                line-height: 1;
                text-overflow: ellipsis;
                overflow: hidden;
                white-space: nowrap;
            }
        }
        .getMore {
            width: 100%;
            height: 45px;
            line-height: 45px;
            border-top: 1px solid #f1f1f1;
            text-align: center;
            color: #3ebfa0;
            /* color: var(--primary-color); */
            font-size: 12px;
        }
        .getMore img {
            width: 4px;
            height: 8px;
            margin-left: 7px;
            object-fit: fill;
        }
    }
    .depart {
        position: relative;
        height: 195px;
        overflow: hidden;
        .depart-inner {
            position: absolute;
            left: 0;
            bottom: -17px;
            right: 0;
            top: 0;
            padding: 10px 12px;
            padding-top: 0;
            overflow-y: hidden;
            overflow-x: scroll;
            .depart-item {
                display: flex;
                div {
                    flex-shrink: 0;
                    font-size: 0;
                    text-align: center;
                    min-width: 25%;
                    // margin-right: 29px;
                    margin-bottom: 20px;
                    img {
                        width: 29px;
                        background-size: 100% auto;
                        margin-bottom: 12px;
                    }
                    .desc {
                        font-size: 14px;
                        text-align: center;
                        line-height: 1;
                    }
                }
            }
        }
        .getMore {
            position: absolute;
            left: 0;
            right: 0;
            bottom: 0;
            height: 45px;
            line-height: 45px;
            border-top: 1px solid #f1f1f1;
            text-align: center;
            color: #3ebfa0;
            /* color: var(--primary-color); */
            font-size: 12px;
        }
        .getMore img {
            width: 4px;
            height: 8px;
            margin-left: 7px;
            object-fit: fill;
        }
    }
}
</style>
