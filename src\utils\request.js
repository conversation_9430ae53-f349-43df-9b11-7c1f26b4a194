/*
 * @Author: Auto generated
 * @Date: 2025-04-12
 * @Description: HTTP请求封装
 */

import axios from 'axios'
import store from '../store'
import auth from './auth'
import util from '../util/util'
import sha256 from 'js-sha256'

// 创建axios实例
const service = axios.create({
  // 基础URL，从环境变量获取
  baseURL: process.env.VUE_APP_BASE_API, // url = base url + request url
  // 请求超时时间
  timeout: 30000,
  // 允许跨域携带cookie
  withCredentials: false
})

// 请求计数器（用于全局loading控制）
let requestCount = 0

// 生成签名
function generateSignature(timestamp, nonce) {
  const signingKey = 'JlolWcxSD3fTdISQkEURIQ=='
  const salt_ = timestamp % 10
  const salt = nonce.substring(salt_)
  const stringSrc = signingKey + timestamp + nonce + salt
  return sha256(stringSrc)
}

// 请求拦截器
service.interceptors.request.use(
  config => {
    // 显示loading
    requestCount++
    util.showLoading()

    // 生成请求头信息
    const timestamp = new Date().getTime()
    const nonce = util.generateUUID()
    const signature = generateSignature(timestamp, nonce)
    const userId = auth.getUserId() || window.localStorage.getItem('userId')

    // 设置公共请求头
    config.headers = {
      timestamp: timestamp,
      nonce: nonce,
      signature: signature,
      id: userId, 
      appType: '3',
      appVersion: 'H5',
      logTraceId: nonce,
      remoteChannel: window.sessionStorage.getItem('hlw_remoteChannel') || 'all',
      ...config.headers
    }

    // 添加认证token - 与http.js保持一致
    const token = localStorage.getItem('access_token')
    if (token) {
      config.headers['X-Auth-Token'] = token
    }

    // 处理POST请求
    if (config.method === 'post' && config.url.indexOf('aliyuncs') === -1) {
      config.headers['Content-Type'] = 'application/json'
    } else if (config.url.indexOf('aliyuncs') > -1) {
      config.headers['content-type'] = 'application/x-www-form-urlencoded'
    }

    // 处理特殊请求类型
    if (config.responseType === 'blob' || config.responseType === 'arraybuffer') {
      // 文件下载请求不需要loading
      requestCount--
      util.hideLoading()
    }

    return config
  },
  error => {
    // 隐藏loading
    requestCount--
    if (requestCount === 0) {
      util.hideLoading()
    }
    return Promise.reject(error)
  }
)

// 响应拦截器
service.interceptors.response.use(
  response => {
    // 隐藏loading
    requestCount--
    if (requestCount === 0) {
      util.hideLoading()
    }

    // 如果设置了responseType: 'all'，直接返回完整的response对象
    if (response.config.responseType === 'all') {
      return response
    }

    // 处理文件下载
    const contentType = response.headers['content-type']
    if (
      (contentType && contentType.includes('application/octet-stream')) ||
      response.config.responseType === 'blob' ||
      response.config.responseType === 'arraybuffer'
    ) {
      return response.data
    }

    // 检查响应头中是否有新的token
    const newToken = response.headers['x-auth-token'] || response.headers['X-Auth-Token']
    if (newToken && newToken !== auth.getToken()) {
      // 如果响应头中有新的token，则更新token
      auth.setToken(newToken, true) // 保存到localStorage
      localStorage.setItem('access_token', newToken) // 直接保存到localStorage
      store.commit('SET_TOKEN', newToken)
    }

    // 处理正常响应 - 按照http.js的逻辑
    if (response.status === 200 && response.data) {
      const res = response.data
      
      // 登录失效处理
      if (res.code === "401") {
        // 保存当前页面路径到sessionStorage，用于登录后跳转
        const currentPath = window.location.hash.replace('#', '') || '/'
        window.sessionStorage.setItem("acURL", currentPath)
        util.loginOut()
        return Promise.reject(new Error(res.msg || '登录失效'))
      } 
      // 成功响应
      else if (res.code == "200") {
        return res
      } 
      // 其他错误
      else {
        util.showToast(res.msg || '请求失败，请稍后再试', 3000)
        return Promise.reject(new Error(res.msg || '请求失败'))
      }
    } else {
      return Promise.reject(response)
    }
  },
  error => {
    // 隐藏loading
    requestCount--
    if (requestCount === 0) {
      util.hideLoading()
    }

    // 按照http.js的错误处理逻辑
    if (error.response) {
      const { status } = error.response

      // 处理服务繁忙 - 与http.js保持一致
      if (status == '503' || status == '429' || status == '598') {
        util.showToast("活动太火爆啦，请稍后再试~", 3000)
        return Promise.reject(error)
      } 
      // 处理登录失效
      else if (status == '401') {
        // 保存当前页面路径到sessionStorage，用于登录后跳转
        const currentPath = window.location.hash.replace('#', '') || '/'
        window.sessionStorage.setItem("acURL", currentPath)
        util.loginOut()
        return Promise.reject(error)
      } 
      // 其他错误
      else {
        return Promise.reject(error)
      }
    } else {
      // 网络错误
      return Promise.reject(error)
    }
  }
)

/**
 * 封装请求方法
 * 支持两种调用方式：
 * 1. request(url, data, method, options)
 * 2. request({ url, method, data, ...options })
 * @param {string|object} url - 请求地址或配置对象
 * @param {object} data - 请求参数
 * @param {string} method - 请求方法
 * @param {object} options - 其他配置
 * @returns {Promise}
 */
const request = (url, data = {}, method = 'get', options = {}) => {
  // 支持对象形式调用
  if (typeof url === 'object') {
    const config = url;
    url = config.url;
    data = config.data || {};
    method = config.method || 'get';
    options = { ...config };
    delete options.url;
    delete options.data;
    delete options.method;
  }
  // 处理完整URL
  let requestUrl = url
  if (!url.includes('https://') && !url.includes('http://')) {
    // 根据环境和URL前缀调整基础URL
    if (process.env.VUE_APP_RUN_ENV === 'stabletest' && url.indexOf('/inthos/') > -1) {
      requestUrl = process.env.VUE_APP_URL + '/rc' + url
    } else if (url.indexOf('/api/') === 0) {
      // 对于/api/开头的请求，直接使用VUE_APP_URL + url
      requestUrl = process.env.VUE_APP_URL + url
    } else {
      // 其他请求使用baseURL（即VUE_APP_BASE_API）+ url的方式
      // 由于service实例已经配置了baseURL，这里直接使用相对路径即可
      requestUrl = url
    }
  }

  // 合并配置
  const config = {
    url: requestUrl,
    method,
    headers: options.headers || {},
    ...options
  }

  // 根据请求方法设置数据
  if (method === 'get') {
    config.params = data
  } else {
    // POST请求序列化数据
    config.data = typeof data === 'object' ? JSON.stringify(data) : data
  }

  return service(config)
}

// 导出常用请求方法
export default request
export const get = (url, params = {}, options = {}) => {
  return request(url, params, 'get', options)
}
export const post = (url, data = {}, options = {}) => {
  return request(url, data, 'post', options)
}
export const put = (url, data = {}, options = {}) => {
  return request(url, data, 'put', options)
}
export const del = (url, data = {}, options = {}) => {
  return request(url, data, 'delete', options)
}

// 导出文件下载方法
export const downloadFile = (url, params = {}, filename = '') => {
  return request(url, params, 'get', {
    responseType: 'blob'
  }).then(blob => {
    // 创建下载链接
    const link = document.createElement('a')
    const href = window.URL.createObjectURL(blob)
    link.href = href
    link.download = filename || 'download'
    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)
    window.URL.revokeObjectURL(href)
  })
}
