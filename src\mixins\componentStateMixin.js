/**
 * 组件状态检查混入
 * 用于在异步操作中检查组件是否已销毁，防止在已销毁的组件上执行操作
 */

export default {
  data() {
    return {
      // 组件销毁标志
      _componentDestroyed: false
    };
  },

  beforeDestroy() {
    // 设置销毁标志
    this._componentDestroyed = true;
    console.log(`${this.$options.name || 'Component'}: 组件即将销毁，设置销毁标志`);
  },

  methods: {
    /**
     * 检查组件是否已销毁
     * @returns {boolean} true表示已销毁，false表示未销毁
     */
    isComponentDestroyed() {
      return (
        this._componentDestroyed ||
        !this.$el ||
        this._isDestroyed ||
        this._isBeingDestroyed ||
        !this.$el.parentNode
      );
    },

    /**
     * 安全执行异步操作
     * 在操作前后都会检查组件状态
     * @param {Function} asyncFn 异步函数
     * @param {string} operationName 操作名称（用于日志）
     * @returns {Promise} 异步操作的结果
     */
    async safeAsyncOperation(asyncFn, operationName = '异步操作') {
      // 操作前检查
      if (this.isComponentDestroyed()) {
        console.log(`组件已销毁，取消${operationName}`);
        return null;
      }

      try {
        console.log(`开始执行${operationName}`);
        const result = await asyncFn();

        // 操作后检查
        if (this.isComponentDestroyed()) {
          console.log(`组件已销毁，忽略${operationName}结果`);
          return null;
        }

        console.log(`${operationName}执行成功`);
        return result;
      } catch (error) {
        // 即使出错也要检查组件状态
        if (this.isComponentDestroyed()) {
          console.log(`组件已销毁，忽略${operationName}错误`);
          return null;
        }

        console.error(`${operationName}执行失败:`, error);
        throw error;
      }
    },

    /**
     * 安全执行回调函数
     * 在执行前检查组件状态
     * @param {Function} callback 回调函数
     * @param {string} callbackName 回调名称（用于日志）
     * @param {...any} args 回调函数的参数
     */
    safeCallback(callback, callbackName = '回调', ...args) {
      if (this.isComponentDestroyed()) {
        console.log(`组件已销毁，忽略${callbackName}`);
        return;
      }

      try {
        return callback.apply(this, args);
      } catch (error) {
        console.error(`${callbackName}执行失败:`, error);
        throw error;
      }
    },

    /**
     * 安全设置数据
     * 在设置前检查组件状态
     * @param {string} key 数据键名
     * @param {any} value 数据值
     * @param {string} description 描述（用于日志）
     */
    safeSetData(key, value, description = '数据') {
      if (this.isComponentDestroyed()) {
        console.log(`组件已销毁，忽略${description}设置: ${key}`);
        return false;
      }

      try {
        this[key] = value;
        console.log(`${description}设置成功: ${key}`);
        return true;
      } catch (error) {
        console.error(`${description}设置失败: ${key}`, error);
        return false;
      }
    },

    /**
     * 安全执行 $nextTick
     * @param {Function} callback 回调函数
     * @param {string} description 描述（用于日志）
     */
    safeNextTick(callback, description = 'nextTick操作') {
      if (this.isComponentDestroyed()) {
        console.log(`组件已销毁，取消${description}`);
        return;
      }

      this.$nextTick(() => {
        if (this.isComponentDestroyed()) {
          console.log(`组件已销毁，忽略${description}`);
          return;
        }

        try {
          callback.call(this);
        } catch (error) {
          console.error(`${description}执行失败:`, error);
        }
      });
    },

    /**
     * 安全执行定时器操作
     * @param {Function} callback 回调函数
     * @param {number} delay 延迟时间
     * @param {string} description 描述（用于日志）
     * @returns {number|null} 定时器ID，如果组件已销毁则返回null
     */
    safeTimeout(callback, delay, description = '定时器操作') {
      if (this.isComponentDestroyed()) {
        console.log(`组件已销毁，取消${description}`);
        return null;
      }

      return setTimeout(() => {
        if (this.isComponentDestroyed()) {
          console.log(`组件已销毁，忽略${description}`);
          return;
        }

        try {
          callback.call(this);
        } catch (error) {
          console.error(`${description}执行失败:`, error);
        }
      }, delay);
    },

    /**
     * 安全执行间隔器操作
     * @param {Function} callback 回调函数
     * @param {number} interval 间隔时间
     * @param {string} description 描述（用于日志）
     * @returns {number|null} 间隔器ID，如果组件已销毁则返回null
     */
    safeInterval(callback, interval, description = '间隔器操作') {
      if (this.isComponentDestroyed()) {
        console.log(`组件已销毁，取消${description}`);
        return null;
      }

      return setInterval(() => {
        if (this.isComponentDestroyed()) {
          console.log(`组件已销毁，忽略${description}`);
          return;
        }

        try {
          callback.call(this);
        } catch (error) {
          console.error(`${description}执行失败:`, error);
        }
      }, interval);
    }
  }
};
