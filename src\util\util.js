/*
 * @Author: shenpp
 * @Date: 2023-05-22
 * @LastEditTime: 2025-04-08 18:38:16
 * @Description:
 */
import { Dialog, Toast } from "vant";
const util = {
    // 退登
    loginOut() {
        this.showToast("登录失效啦，即将重新登录~", 3000);
        window.localStorage.removeItem("access_token")
        window.localStorage.removeItem("userInfo")
        // 保存当前页面路径而不是完整URL，避免URL重复问题
        const currentPath = window.location.hash.replace('#', '') || '/'
        window.sessionStorage.setItem("acURL", currentPath)
        window.location.href = "#/login"
    },
    // 获取传参
    getQueryStringHash(name) {
        let result = "";
        if (window.location.hash.includes("?")) {
            window.location.hash
                .split("?")[1]
                .split("&")
                .forEach((val) => {
                    if (val.includes(name)) {
                        result = val.substring(name.length + 1);
                    }
                });
        }
        // console.log('result', result)
        return result;
    },
    showToast(
        message = "网络连接超时，请稍后再试~",
        duration = 1500,
        forbidClick = true
    ) {
        Toast({
            duration,
            forbidClick,
            message,
        });
    },
    showLoading({
        message = "努力加载中，请稍后",
        forbidClick = true,
        loadingType = "spinner",
        duration = 0,
    } = {}) {
        Toast.loading({
            message,
            forbidClick,
            loadingType,
            duration,
        });
    },
    hideLoading() {
        Toast.clear();
    },
    openDialogAlert(title, content, fun, text, className) {
        return Dialog.alert({
            title,
            className,
            message: content,
            confirmButtonText: text || "确认",
            confirmButtonColor: "#07C160",
        })
            .then(() => {
                if (fun) {
                    Dialog.close();
                    fun();
                }
            })
            .catch(() => {
                // this.showToast('取消')
            });
    },
    generateUUID() {
        var d = new Date().getTime();
        if (window.performance && typeof window.performance.now === "function") {
            d += performance.now(); //use high-precision timer if available
        }
        var uuid = "xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx".replace(
            /[xy]/g,
            function (c) {
                var r = (d + Math.random() * 16) % 16 | 0;
                d = Math.floor(d / 16);
                return (c == "x" ? r : (r & 0x3) | 0x8).toString(16);
            }
        );
        return uuid;
    },
    // loginOut() {
    //     console.log("退出登录");

    //     // 退出登录
    //     window.localStorage.clear();
    //     window.sessionStorage.clear();
    //     if (
    //         navigator.userAgent.indexOf("AliApp") > -1 &&
    //         window.localStorage.getItem("interHosp_origin") != "szMini"
    //     ) {
    //         my.postMessage({
    //             action: "clearStorage",
    //         });
    //         my.navigateTo({
    //             url: "/pages/index/index",
    //         });
    //         // window.location.replace("#/home");
    //         return;
    //     }
    //     if (navigator.userAgent.toLowerCase().indexOf("toutiaomicroapp") > -1) {
    //         // 清除后，需跳转到小程序页面，小程序页面缓存也要清除
    //         // let setTime = setInterval(() => {
    //         //     console.log("跳转到首页");
    //         //     clearInterval(setTime);
    //         //     tt.miniProgram.navigateTo({
    //         //         url: "/pages/wxLogOut/wxLogOut",
    //         //     });
    //         // }, 500);
    //         return
    //     }

    //     // 清除后，需跳转到小程序页面，小程序页面缓存也要清除
    //     let setTime = setInterval(() => {
    //         console.log("跳转到首页");
    //         clearInterval(setTime);
    //         wx.miniProgram.navigateTo({
    //             url: "/pages/wxLogOut/wxLogOut",
    //         });
    //     }, 500);
    // }
};

export default util;
