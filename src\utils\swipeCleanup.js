/**
 * 轮播组件清理工具
 * 用于在页面切换时清理所有轮播组件的自动播放定时器
 */

class SwipeCleanupManager {
  constructor() {
    this.activeSwipes = new Set();
    this.cleanupCallbacks = new Map();
  }

  /**
   * 注册轮播组件
   * @param {Object} component Vue组件实例
   * @param {string} id 组件唯一标识
   */
  register(component, id = null) {
    const componentId = id || this.generateId();
    console.log(`SwipeCleanup: 注册轮播组件 ${componentId}`);
    
    this.activeSwipes.add({
      id: componentId,
      component: component,
      timestamp: Date.now()
    });

    return componentId;
  }

  /**
   * 注销轮播组件
   * @param {string} id 组件唯一标识
   */
  unregister(id) {
    console.log(`SwipeCleanup: 注销轮播组件 ${id}`);
    
    for (const swipe of this.activeSwipes) {
      if (swipe.id === id) {
        this.activeSwipes.delete(swipe);
        break;
      }
    }
    
    if (this.cleanupCallbacks.has(id)) {
      this.cleanupCallbacks.delete(id);
    }
  }

  /**
   * 清理所有轮播组件
   */
  cleanupAll() {
    console.log(`SwipeCleanup: 开始清理所有轮播组件，共 ${this.activeSwipes.size} 个`);
    
    for (const swipe of this.activeSwipes) {
      try {
        this.cleanupSwipe(swipe.component);
      } catch (error) {
        console.error(`SwipeCleanup: 清理组件 ${swipe.id} 时出错:`, error);
      }
    }
    
    // 清理DOM中的轮播组件
    this.cleanupDOMSwipes();
    
    // 清空注册列表
    this.activeSwipes.clear();
    this.cleanupCallbacks.clear();
    
    console.log('SwipeCleanup: 所有轮播组件清理完成');
  }

  /**
   * 清理单个轮播组件
   * @param {Object} component Vue组件实例
   */
  cleanupSwipe(component) {
    if (!component || !component.$el) {
      return;
    }

    try {
      // 如果组件有自定义清理方法，优先使用
      if (typeof component.cleanupSwipe === 'function') {
        component.cleanupSwipe();
        return;
      }

      // 查找组件内的轮播元素
      const swipeElements = component.$el.querySelectorAll('.van-swipe');
      swipeElements.forEach((element, index) => {
        console.log(`SwipeCleanup: 清理第 ${index + 1} 个轮播元素`);
        
        const vueComponent = element.__vue__;
        if (vueComponent) {
          // 停止自动播放
          if (typeof vueComponent.stopAutoplay === 'function') {
            vueComponent.stopAutoplay();
          }
          
          // 清理定时器
          this.clearSwipeTimers(vueComponent);
        }
      });
    } catch (error) {
      console.error('SwipeCleanup: 清理轮播组件时出错:', error);
    }
  }

  /**
   * 清理DOM中的所有轮播组件
   */
  cleanupDOMSwipes() {
    try {
      const allSwipes = document.querySelectorAll('.van-swipe');
      console.log(`SwipeCleanup: 发现 ${allSwipes.length} 个DOM轮播元素`);
      
      allSwipes.forEach((element, index) => {
        const vueComponent = element.__vue__;
        if (vueComponent) {
          console.log(`SwipeCleanup: 清理DOM轮播元素 ${index + 1}`);
          
          // 停止自动播放
          if (typeof vueComponent.stopAutoplay === 'function') {
            vueComponent.stopAutoplay();
          }
          
          // 清理定时器
          this.clearSwipeTimers(vueComponent);
        }
      });
    } catch (error) {
      console.error('SwipeCleanup: 清理DOM轮播组件时出错:', error);
    }
  }

  /**
   * 清理轮播组件的定时器
   * @param {Object} swipeComponent 轮播组件实例
   */
  clearSwipeTimers(swipeComponent) {
    const timerNames = [
      'autoplayTimer',
      'timer',
      'autoTimer',
      'playTimer',
      'intervalId',
      'timeoutId'
    ];

    timerNames.forEach(timerName => {
      if (swipeComponent[timerName]) {
        clearInterval(swipeComponent[timerName]);
        clearTimeout(swipeComponent[timerName]);
        swipeComponent[timerName] = null;
        console.log(`SwipeCleanup: 清理定时器 ${timerName}`);
      }
    });
  }

  /**
   * 生成唯一ID
   */
  generateId() {
    return `swipe_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  /**
   * 获取活跃的轮播组件数量
   */
  getActiveCount() {
    return this.activeSwipes.size;
  }

  /**
   * 设置页面切换时的清理回调
   */
  onPageChange(callback) {
    if (typeof callback === 'function') {
      this.cleanupAll();
      callback();
    }
  }
}

// 创建全局实例
const swipeCleanupManager = new SwipeCleanupManager();

// 在页面可见性变化时清理
document.addEventListener('visibilitychange', () => {
  if (document.hidden) {
    console.log('SwipeCleanup: 页面隐藏，清理轮播组件');
    swipeCleanupManager.cleanupAll();
  }
});

// 在页面卸载前清理
window.addEventListener('beforeunload', () => {
  console.log('SwipeCleanup: 页面即将卸载，清理轮播组件');
  swipeCleanupManager.cleanupAll();
});

// 在页面隐藏时清理（移动端）
window.addEventListener('pagehide', () => {
  console.log('SwipeCleanup: 页面隐藏，清理轮播组件');
  swipeCleanupManager.cleanupAll();
});

export default swipeCleanupManager;
